package csim.model.api;

import java.util.Collection;

public interface ModelContext<T extends ModelObject> {

    /**
     * 获取ID
     * 
     * @return
     */
    String getId();
    /**
     * 获取模型对象
     * @return
     */
    T getModelObject();


    /**
     * 获取父级模型
     * 只能在同一个平台模型内部使用
     * @return
     */
    ModelContext getParentModelContext();

    /**
     * 获取组件模型
     *
     * @return
     */
    Collection<ModelContext> getChildModelContext();


    /**
     * 获取兄弟组件模型, 包括自身
     *
     * @return
     */
    default Collection<ModelContext> getSiblingModelContext(){
        return getParentModelContext().getChildModelContext();
    }



    /**
     * 当前仿真时间
     * @return
     */
    long getCurrentTime();

    /**
     * 上一帧时间
     * @return
     */
    long getLastTickTime();
}
