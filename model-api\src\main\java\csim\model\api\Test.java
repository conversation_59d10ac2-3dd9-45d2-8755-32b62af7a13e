package csim.model.api;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.regex.*;

public class Test {
    public static void main(String[] args) throws IOException {
        Path rootPath = Paths.get("D:\\project\\ai\\knowledge-agent\\share\\src\\main\\java\\isimado\\knowledgeagent\\share\\entity\\dto");


        // 处理注解
        Files.walkFileTree(rootPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (file.toString().endsWith(".java")) {
                    String content = new String(Files.readAllBytes(file));

                    Pattern pattern = Pattern.compile("\\{\\{(.*?)\\}\\}");
                    // 移除类上的XML注解
                    content = content.replaceAll(
                        "@XmlEnum", "");

                    Files.write(file, content.getBytes());
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        System.out.println("注解处理完成");
    }
}
