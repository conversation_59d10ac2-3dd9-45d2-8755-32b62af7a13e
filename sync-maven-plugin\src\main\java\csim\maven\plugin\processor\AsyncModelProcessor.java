package csim.maven.plugin.processor;

import com.google.auto.service.AutoService;
import csim.maven.plugin.annotation.AsyncModel;
import com.sun.source.util.TreePath;
import com.sun.source.util.Trees;
import com.sun.tools.javac.parser.ParserFactory;
import com.sun.tools.javac.processing.JavacProcessingEnvironment;
import com.sun.tools.javac.tree.JCTree;
import com.sun.tools.javac.tree.Pretty;
import com.sun.tools.javac.tree.TreeMaker;
import com.sun.tools.javac.util.Context;
import com.sun.tools.javac.util.Names;
import csim.maven.plugin.util.ModuleUtils;

import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.Set;

@SupportedAnnotationTypes("csim.maven.plugin.annotation.AsyncModel")
@SupportedSourceVersion(SourceVersion.RELEASE_21)
@AutoService(Processor.class)
public class AsyncModelProcessor extends AbstractProcessor {


    static {
        ModuleUtils.addOpensForClass(AsyncModelProcessor.class, "jdk.compiler",
                "com.sun.tools.javac.code",
                "com.sun.tools.javac.comp",
                "com.sun.tools.javac.file",
                "com.sun.tools.javac.main",
                "com.sun.tools.javac.util",
                "com.sun.tools.javac.tree",
                "com.sun.tools.javac.processing",
                "com.sun.tools.javac.parser"
        );
    }

    private Trees trees;
    private TreeMaker treeMaker;
    private Names names;
    // 创建 ParserFactory 并解析源码
    ParserFactory parserFactory;

    private int __csim__;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        processingEnv = jbUnwrap(ProcessingEnvironment.class, processingEnv);
        super.init(processingEnv);
        trees = Trees.instance(processingEnv);
        Context context = ((JavacProcessingEnvironment) processingEnv).getContext();
        treeMaker = TreeMaker.instance(context);
        names = Names.instance(context);
        parserFactory = ParserFactory.instance(context);
    }

    /**
     * 兼容IDEA
     *
     * @param iface
     * @param wrapper
     * @param <T>
     * @return
     */
    private static <T> T jbUnwrap(Class<? extends T> iface, T wrapper) {
        T unwrapped = null;
        try {
            final Class<?> apiWrappers = wrapper.getClass().getClassLoader().loadClass("org.jetbrains.jps.javac.APIWrappers");
            final Method unwrapMethod = apiWrappers.getDeclaredMethod("unwrap", Class.class, Object.class);
            unwrapped = iface.cast(unwrapMethod.invoke(null, iface, wrapper));
        } catch (Throwable ignored) {
        }
        return unwrapped != null ? unwrapped : wrapper;
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        for (Element element : roundEnv.getElementsAnnotatedWith(AsyncModel.class)) {
            if (element instanceof TypeElement) {
                TypeElement typeElement = (TypeElement) element;
                TreePath treePath = trees.getPath(typeElement);

                JCTree.JCCompilationUnit unit = (JCTree.JCCompilationUnit) treePath.getCompilationUnit();


                if (treePath != null) {
                    JCTree.JCClassDecl classDecl = (JCTree.JCClassDecl) treePath.getLeaf();
                    new AsyncModelTreeScanner(trees, treeMaker, names).scan(classDecl, null);
                }

                PrintWriter writer = new PrintWriter(System.out);
                Pretty prettyPrinter = new Pretty(writer, false);
                try {
                    prettyPrinter.printUnit(unit, null);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                writer.flush();

            }
        }
        return true;
    }

}