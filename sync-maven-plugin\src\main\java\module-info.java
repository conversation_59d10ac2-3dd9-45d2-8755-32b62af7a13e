module csim.sync.maven.plugin {
    requires java.base;
    requires jdk.compiler;
    requires java.compiler;
    requires jdk.unsupported; // 用于 sun.misc.Unsafe

    // 这些依赖可能不是模块化的，使用 automatic modules
    requires static com.google.auto.service;
    requires static com.google.common;

    // 导出注解处理器服务
    provides javax.annotation.processing.Processor with
        csim.maven.plugin.processor.AsyncModelProcessor,
        csim.maven.plugin.processor.BindModelProcessor;

    // 导出公共 API
    exports csim.maven.plugin.annotation;
}