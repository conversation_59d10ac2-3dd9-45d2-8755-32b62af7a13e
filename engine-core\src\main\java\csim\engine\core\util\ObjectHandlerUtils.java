package csim.engine.core.util;

import csim.engine.core.model.DefaultModelObjectHandler;
import csim.model.api.ModelObject;
import csim.model.api.ModelObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Slf4j
public class ObjectHandlerUtils {

    //ModelObject类 -> Handler类
    static Map<Class, Class> handlerMap = new HashMap<>();

    static {
        try {
            Enumeration<URL> resources = ObjectHandlerUtils.class.getClassLoader().getResources("handlers.properties");
            while (resources.hasMoreElements()) {
                URL url = resources.nextElement();
                try (InputStream is = url.openStream()) {
                    Properties prop = new Properties();
                    prop.load(is);
                    Enumeration<Object> keys = prop.keys();
                    while (keys.hasMoreElements()) {
                        String modelObjectClassName = (String) keys.nextElement();
                        String objectHandlerClassName = prop.getProperty(modelObjectClassName);
                        Class old = handlerMap.put(Class.forName(modelObjectClassName), Class.forName(objectHandlerClassName));
                        Assert.isNull(old, () -> "[" + modelObjectClassName + "]存在重复的处理器:" + objectHandlerClassName + ", " + old.getName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("加载handlers.properties异常: {}", e.getMessage(), e);
        }
    }

    public static ModelObjectHandler getModelObjectHandler(ModelObject modelObject) {
        Class clazz = modelObject.getClass();
        Class aClass = handlerMap.computeIfAbsent(clazz, k -> doGetModelObjectHandler(clazz));
        try {
            Constructor constructor = aClass.getConstructor();
            return (ModelObjectHandler) constructor.newInstance();
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(aClass.getName()+" 没有无参构造器.");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private synchronized static Class doGetModelObjectHandler(Class clazz) {
        while (clazz != null) {
            Class aClass = handlerMap.get(clazz);
            if (aClass != null) {
                return aClass;
            }
            clazz = clazz.getSuperclass();
        }
        return DefaultModelObjectHandler.class;
    }
}
