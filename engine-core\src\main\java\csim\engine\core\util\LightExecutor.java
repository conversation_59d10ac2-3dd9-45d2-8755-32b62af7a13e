package csim.engine.core.util;

import io.netty.channel.DefaultEventLoop;
import io.netty.channel.EventLoop;
import io.netty.util.concurrent.EventExecutorGroup;
import io.netty.util.concurrent.SingleThreadEventExecutor;

import java.util.concurrent.ThreadFactory;

public class LightExecutor extends DefaultEventLoop {

    public LightExecutor() {
        super(new ThreadFactory(){
            @Override
            public Thread newThread(Runnable r) {
                return Thread.ofVirtual().unstarted(r);
            }
        });
    }

}


