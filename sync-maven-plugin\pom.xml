<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>csim</groupId>
        <artifactId>csim-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>csim-sync-maven-plugin</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
            <version>1.1.1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.google.auto</groupId>
            <artifactId>auto-common</artifactId>
            <version>1.2.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.code=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.comp=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.file=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.main=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.model=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.parser=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.processing=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.tree=csim.sync.maven.plugin</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.util=csim.sync.maven.plugin</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>