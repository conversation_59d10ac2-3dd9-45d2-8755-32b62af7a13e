<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>csim</groupId>
        <artifactId>csim-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>csim-sync-maven-plugin</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
            <version>1.1.1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.google.auto</groupId>
            <artifactId>auto-common</artifactId>
            <version>1.2.1</version>
        </dependency>

        <!-- 添加 Guava 依赖 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.3-jre</version>
        </dependency>

        <!-- 添加 JSR-305 注解依赖 -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <!-- 添加对 JDK 编译器内部 API 的访问权限 -->
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>

                        <!-- 添加对 JDK 内部 unsafe API 的访问权限 -->
                        <arg>--add-exports</arg>
                        <arg>java.base/sun.misc=ALL-UNNAMED</arg>

                        <!-- 添加 opens 权限以支持反射访问 -->
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>java.base/sun.misc=ALL-UNNAMED</arg>
                        <arg>--add-opens</arg>
                        <arg>java.base/java.lang=ALL-UNNAMED</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>