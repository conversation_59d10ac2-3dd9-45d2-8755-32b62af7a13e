package csim.engine.core.model;

import csim.engine.core.event.TickComplete;
import csim.maven.plugin.annotation.AsyncModel;
import csim.maven.plugin.annotation.SyncMethod;
import csim.model.api.ModelContext;
import csim.model.api.ModelObject;
import csim.model.api.ModelObjectHandler;
import io.netty.channel.EventLoop;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@AsyncModel
public class AtomicModel<T extends ModelObject> implements ModelContext<T> {
    /**
     * 当前模型实体执行器
     */
    protected final EventLoop executor;

    private final AtomicModel parent;

    private final Set<AtomicModel> children = new HashSet<>();

    private TreeMap<Long, Set<AtomicModel>> scheduleEvent = new TreeMap<>();

    /**
     * 尚未被调度的对象
     */
    protected final Set<AtomicModel> unScheduled = new HashSet<>();

    @Getter
    private final String id;

    @Override
    @SyncMethod
    public String getId(){
        return id;
    }

    /**
     * 模型实体
     */
    private final T modelObject;

    private final ModelObjectHandler handler;

    /**
     * 上次调度的时间
     */
    private Long lastTickTime;

    /**
     * 当前时间
     */
    private Long currentTime;

    protected final long simStartTime;

    protected AtomicModel(EventLoop executor,
                          AtomicModel parent,
                          T modelObject,
                          String id,
                          ModelObjectHandler handler,
                          long simStartTime) {
        Assert.notNull(executor, "executor must not be null");
        Assert.notNull(modelObject, "modelObject must not be null");
        Assert.hasText(id, "id must not be null");
        this.executor = executor;
        this.parent = parent;
        this.modelObject = modelObject;
        this.id = id;
        this.handler = handler;
        this.handler.setContext(this);
        this.simStartTime = simStartTime;
    }

    protected void addObject(AtomicModel child) {
        unScheduled.add(child);
        this.children.add(child);
        child.onJoinSimulation(getCurrentTime());
    }
    /**
     * 当前推演时间
     *
     * @return
     */
    @Override
    public long getCurrentTime() {
        if (currentTime == null) {
            return simStartTime;
        }
        return currentTime;
    }

    /**
     * 上次调度时间
     *
     * @return
     */
    @Override
    public long getLastTickTime() {
        return lastTickTime;
    }

    public T getModelObject() {
        return modelObject;
    }

    @Override
    public ModelContext getParentModelContext() {
        return this.parent;
    }

    @Override
    public Collection<ModelContext> getChildModelContext() {
        return Set.copyOf(children);
    }

    public void onJoinSimulation(long logicTime){
        this.handler.onJoinSimulation(logicTime);
    }
    //当前被调度
    private Set<String> ticked = new HashSet<>();

    private int tickCount = -1;

    /**
     * 每秒调度
     *
     * @param logicTime
     */
    public void tick(long logicTime) {
        this.lastTickTime = this.currentTime;
        this.currentTime = logicTime;
        try {
            if (handler != null) {
                handler.tick();
            }

            if (!unScheduled.isEmpty()) {
                //尚未调度的实体 默认一秒后调度
                scheduleEvent.computeIfAbsent(currentTime, k -> new HashSet<>()).addAll(unScheduled);
                unScheduled.clear();
            }
            Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();
            if (entry != null && entry.getKey().equals(currentTime)) {
                Set<AtomicModel> willTicked = scheduleEvent.remove(currentTime);
                tickCount = willTicked.size();
                for (AtomicModel model : willTicked) {
                    model.tick(currentTime);
                }
            } else {
                onTickComplete();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    void onChildTickComplete(TickComplete event) {
        tickCount--;
        scheduleEvent.computeIfAbsent(event.nextTime(), k -> new HashSet<>()).add(event.model());
        if (tickCount == 0) {
            onTickComplete();
        }
    }

    protected void onTickComplete() {
        long next = next();
        parent.onChildTickComplete(new TickComplete(this, next));
    }


    /**
     * 下一帧
     *
     * @return
     */
    long next() {
        if (scheduleEvent.isEmpty()) {
            return getCurrentTime() + modelObject.getFrameLength();
        }
        Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();
        Long key = entry.getKey();
        return key;
    }

}