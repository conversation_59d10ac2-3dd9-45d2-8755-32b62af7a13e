package csim.engine.core;

import csim.engine.core.model.RootModel;
import csim.engine.core.util.ModelJSONDTO;
import csim.engine.core.util.ModelJsonUtils;
import io.netty.channel.DefaultEventLoopGroup;
import io.netty.channel.EventLoopGroup;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

public class Bootstrap {


    static RootModel rootModel;
    @SneakyThrows
    public static void main(String[] args) {

        System.out.println("Hello World!");
        EventLoopGroup boss = new DefaultEventLoopGroup(1);

        rootModel = new RootModel(boss.next(), 0, 1000*600);


        loadScn();

        Thread.sleep(2000);
        rootModel.start(0);
    }

    @SneakyThrows
    public static void loadScn(){
        File dir = new File("D:\\project\\csim\\properties1");
        File[] files = dir.listFiles();

        List<ModelJSONDTO> result = Collections.synchronizedList(new ArrayList<>());

        CountDownLatch latch = new CountDownLatch(files.length);
        for (File file : files) {
            if (file.getName().endsWith(".json")) {
                Thread.startVirtualThread(()-> {
                    String content = null;
                    try {
                        content = Files.readString(file.toPath());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    ModelJSONDTO model = ModelJsonUtils.parse(content, ModelJSONDTO.class);
                    if (!StringUtils.hasText(model.getObjectInstanceHandle())) {
                        model.setObjectInstanceHandle(UUID.randomUUID().toString());
                    }


                    List<ModelJSONDTO> components = model.getComponents();
                    if (components != null) {
                        components.forEach(component -> {
                            if (!StringUtils.hasText(component.getObjectInstanceHandle())) {
                                component.setObjectInstanceHandle(UUID.randomUUID().toString());
                            }
                        });
                    }

                    result.add(model);
                    latch.countDown();
                });

            }
        }
        latch.await();

        long startTime = System.currentTimeMillis();
        result.forEach(model -> {
            rootModel.addObject(model.getObjectInstanceHandle(), model.toModelObject());
            List<ModelJSONDTO> components = model.getComponents();
            if (components != null) {
                components.forEach(component -> {
                    rootModel.addObject(model.getObjectInstanceHandle(), component.getObjectInstanceHandle(), component.toModelObject());
                });
            }
        });
        System.out.println("loadScn time: " + (System.currentTimeMillis() - startTime));

    }
}
