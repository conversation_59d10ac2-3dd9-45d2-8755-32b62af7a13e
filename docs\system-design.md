# CSIM分布式仿真引擎设计文档

## 系统概述
CSIM是一个基于Spring Boot的分布式仿真引擎系统，采用模块化设计，支持层次化的模型结构和灵活的调度机制。系统使用Java 21开发，利用现代并发特性（如虚拟线程）来提升性能。

## 模块结构
系统主要包含两个核心模块：
1. model-api：定义核心接口和模型规范
2. engine-core：实现仿真引擎的核心功能

### 类图结构
```mermaid
classDiagram
    class ModelObject {
        <<interface>>
        +getFrameLength() Integer
    }
    
    class ModelContext {
        <<interface>>
        +getId() String
        +getModelObject() T
        +getParentModelContext() ModelContext
        +getChildModelContext() Collection~ModelContext~
        +getCurrentTime() long
        +getLastTickTime() long
    }
    
    class ModelObjectHandler {
        <<abstract>>
        #context ModelContext
        +onJoinSimulation(long)
        +tick()* void
        +onMessage(Object)* void
    }
    
    class AtomicModel {
        -executor EventLoop
        -parent AtomicModel
        -children Set~AtomicModel~
        -scheduleEvent TreeMap~Long, Set~AtomicModel~~
        +tick(long) void
        +next() long
    }
    
    class RootModel {
        -models Map~String, AtomicModel~
        -status EngineStatus
        -speed double
        +addObject(String, ModelObject) void
        +start(long) void
    }

    ModelContext ..> ModelObject
    ModelObjectHandler --> ModelContext
    AtomicModel ..|> ModelContext
    RootModel --|> AtomicModel
```

### 核心组件职责

1. **ModelObject接口**
   - 定义仿真对象的基本接口
   - 提供获取仿真帧长度的方法
   - 作为所有仿真实体的顶层抽象

2. **ModelContext接口**
   - 管理模型的上下文信息
   - 维护模型间的层次关系
   - 提供时间管理相关方法
   - 支持父子模型间的关联访问

3. **ModelObjectHandler抽象类**
   - 处理具体的仿真业务逻辑
   - 提供生命周期回调方法
   - 管理消息处理机制
   - 实现仿真调度响应

4. **AtomicModel类**
   - 实现基础的模型功能
   - 管理子模型集合
   - 处理调度事件
   - 维护仿真时间

5. **RootModel类**
   - 作为仿真系统的顶层节点
   - 管理全局模型实例
   - 控制仿真进程
   - 实现速度控制

## 调度流程
系统采用事件驱动的异步调度机制，主要流程如下：

```mermaid
sequenceDiagram
    participant Bootstrap
    participant RootModel
    participant AtomicModel
    participant ModelObjectHandler

    Bootstrap->>RootModel: start(currentTime)
    RootModel->>RootModel: tick(logicTime)
    activate RootModel
    
    loop 对每个调度时间点
        RootModel->>AtomicModel: tick(logicTime)
        activate AtomicModel
        AtomicModel->>ModelObjectHandler: tick()
        
        loop 对子模型
            AtomicModel->>AtomicModel: tick(logicTime)
            AtomicModel->>ModelObjectHandler: tick()
        end
        
        AtomicModel->>AtomicModel: onTickComplete()
        AtomicModel-->>RootModel: onChildTickComplete(tickEvent)
        deactivate AtomicModel
        
        RootModel->>RootModel: onTickComplete()
        RootModel->>RootModel: next()
        RootModel->>RootModel: tick(nextTime)
    end
    deactivate RootModel
```

## 系统特点

### 1. 分层架构
- 清晰的接口定义和职责划分
- 支持复杂的模型层次结构
- 灵活的模型组合机制

### 2. 高性能设计
- 利用虚拟线程实现高效并发
- 基于EventLoop的事件驱动架构
- 异步模型注解优化

### 3. 数据管理
- JSON格式的模型数据加载
- UUID标识模型实例
- 树状结构的模型关系管理

### 4. 时间管理
- 支持实时和加速推演
- 精确的帧同步机制
- 灵活的调度策略

## 扩展性考虑
1. 支持自定义ModelObjectHandler实现特定业务逻辑
2. 可通过配置文件调整系统参数
3. 预留消息处理机制便于后续扩展
4. 模块化设计便于集成新功能